import React, { useState, useEffect } from 'react';
import { PlusIcon, MagnifyingGlassIcon, DocumentArrowDownIcon, DocumentArrowUpIcon } from '@heroicons/react/24/outline';
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import Table from '@/components/common/Table';
import Pagination from '@/components/common/Pagination';
import Loading from '@/components/common/Loading';
import Modal, { ModalBody, ModalFooter } from '@/components/common/Modal';
import Badge from '@/components/common/Badge';
import { useApi } from '@/hooks/useApi';
import { systemService } from '@/services/system';
import { Question, QuestionListParams } from '@/types/system';
import { TableColumn } from '@/types/common';
import QuestionForm from './QuestionForm';
import QuestionDetail from './QuestionDetail';

const QuestionManagement: React.FC = () => {
  // 状态管理
  const [questions, setQuestions] = useState<Question[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [selectedLanguage, setSelectedLanguage] = useState<'zh_CN' | 'en' | ''>('');
  const [sortBy, setSortBy] = useState<string>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  
  // 弹窗状态
  const [formModalOpen, setFormModalOpen] = useState(false);
  const [detailModalOpen, setDetailModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [exportModalOpen, setExportModalOpen] = useState(false);
  const [importModalOpen, setImportModalOpen] = useState(false);
  
  // 选中的问题
  const [selectedQuestion, setSelectedQuestion] = useState<Question | null>(null);
  const [editingQuestion, setEditingQuestion] = useState<Question | null>(null);

  // API调用
  const {
    loading,
    execute: fetchQuestions,
  } = useApi(systemService.getQuestions);

  const {
    loading: deleting,
    execute: deleteQuestion,
  } = useApi(systemService.deleteQuestion);

  const {
    loading: duplicating,
    execute: duplicateQuestion,
  } = useApi(systemService.duplicateQuestion);

  // 加载问题列表
  useEffect(() => {
    loadQuestions();
  }, [currentPage, pageSize, searchKeyword, selectedLanguage, sortBy, sortOrder]);

  const loadQuestions = async () => {
    try {
      const params: QuestionListParams = {
        page: currentPage,
        limit: pageSize,
        sortBy,
        sortOrder,
      };

      if (searchKeyword) {
        params.search = searchKeyword;
      }

      if (selectedLanguage) {
        params.language = selectedLanguage;
      }

      const response = await fetchQuestions(params);
      setQuestions(response.items);
      setTotal(response.total);
    } catch (error) {
      console.error('加载问题列表失败:', error);
    }
  };

  // 搜索处理
  const handleSearch = () => {
    setCurrentPage(1);
    loadQuestions();
  };

  // 重置搜索
  const handleResetSearch = () => {
    setSearchKeyword('');
    setSelectedLanguage('');
    setCurrentPage(1);
  };

  // 排序处理
  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
    setCurrentPage(1);
  };

  // 新增问题
  const handleAdd = () => {
    setEditingQuestion(null);
    setFormModalOpen(true);
  };

  // 编辑问题
  const handleEdit = (question: Question) => {
    setEditingQuestion(question);
    setFormModalOpen(true);
  };

  // 查看详情
  const handleDetail = (question: Question) => {
    setSelectedQuestion(question);
    setDetailModalOpen(true);
  };

  // 删除问题
  const handleDelete = (question: Question) => {
    setSelectedQuestion(question);
    setDeleteModalOpen(true);
  };

  // 确认删除
  const confirmDelete = async () => {
    if (!selectedQuestion) return;

    try {
      await deleteQuestion(selectedQuestion._id);
      setDeleteModalOpen(false);
      setSelectedQuestion(null);
      loadQuestions();
      alert('删除成功！');
    } catch (error) {
      console.error('删除失败:', error);
    }
  };

  // 复制问题
  const handleDuplicate = async (question: Question) => {
    try {
      await duplicateQuestion(question._id);
      loadQuestions();
      alert('复制成功！');
    } catch (error) {
      console.error('复制失败:', error);
    }
  };

  // 表单提交成功回调
  const handleFormSuccess = () => {
    setFormModalOpen(false);
    setEditingQuestion(null);
    loadQuestions();
  };

  // 导出问题
  const handleExport = async (format: 'json' | 'csv', includeAllLanguages: boolean) => {
    try {
      const params = {
        format,
        includeAllLanguages,
        language: selectedLanguage || undefined,
      };
      
      const blob = await systemService.exportQuestions(params);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `questions-${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      setExportModalOpen(false);
    } catch (error) {
      console.error('导出失败:', error);
    }
  };

  // 表格列定义
  const columns: TableColumn<Question>[] = [
    {
      key: 'sketch',
      title: '问题概要',
      dataIndex: 'sketch',
      render: (value: Record<string, string>) => (
        <div className="max-w-xs">
          <div className="text-sm font-medium text-gray-900 truncate">
            {value.zh_CN || value.en || '无标题'}
          </div>
          {value.zh_CN && value.en && (
            <div className="text-xs text-gray-500 truncate mt-1">
              {value.en}
            </div>
          )}
        </div>
      ),
    },
    {
      key: 'languages',
      title: '支持语言',
      dataIndex: 'question',
      width: 120,
      render: (value: Record<string, string>) => (
        <div className="flex flex-wrap gap-1">
          {Object.keys(value).map((lang) => (
            <Badge
              key={lang}
              variant={lang === 'zh_CN' ? 'success' : 'info'}
              size="sm"
            >
              {lang === 'zh_CN' ? '中文' : '英文'}
            </Badge>
          ))}
        </div>
      ),
    },
    {
      key: 'createdAt',
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 160,
      sortable: true,
      render: (value: string) => (
        <span className="text-sm text-gray-900">
          {value ? new Date(value).toLocaleString('zh-CN') : '-'}
        </span>
      ),
    },
    {
      key: 'updatedAt',
      title: '更新时间',
      dataIndex: 'updatedAt',
      width: 160,
      sortable: true,
      render: (value: string) => (
        <span className="text-sm text-gray-900">
          {value ? new Date(value).toLocaleString('zh-CN') : '-'}
        </span>
      ),
    },
    {
      key: 'actions',
      title: '操作',
      dataIndex: '_id',
      width: 200,
      render: (_, record: Question) => (
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="secondary"
            onClick={() => handleDetail(record)}
          >
            查看
          </Button>
          <Button
            size="sm"
            variant="primary"
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            size="sm"
            variant="secondary"
            onClick={() => handleDuplicate(record)}
            loading={duplicating}
          >
            复制
          </Button>
          <Button
            size="sm"
            variant="danger"
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">问题库管理</h1>
          <p className="mt-1 text-sm text-gray-600">
            管理系统中的问题模板和多语言内容
          </p>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="secondary"
            onClick={() => setImportModalOpen(true)}
            icon={<DocumentArrowUpIcon className="h-4 w-4" />}
          >
            导入
          </Button>
          <Button
            variant="secondary"
            onClick={() => setExportModalOpen(true)}
            icon={<DocumentArrowDownIcon className="h-4 w-4" />}
          >
            导出
          </Button>
          <Button
            variant="primary"
            onClick={handleAdd}
            icon={<PlusIcon className="h-4 w-4" />}
          >
            新增问题
          </Button>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="md:col-span-2">
            <Input
              placeholder="搜索问题内容..."
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              fullWidth
            />
          </div>
          <div>
            <select
              value={selectedLanguage}
              onChange={(e) => setSelectedLanguage(e.target.value as 'zh_CN' | 'en' | '')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">全部语言</option>
              <option value="zh_CN">中文</option>
              <option value="en">英文</option>
            </select>
          </div>
          <div className="flex space-x-2">
            <Button
              variant="primary"
              onClick={handleSearch}
              fullWidth
            >
              搜索
            </Button>
            <Button
              variant="secondary"
              onClick={handleResetSearch}
            >
              重置
            </Button>
          </div>
        </div>
      </div>

      {/* 问题列表 */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">
              问题列表 ({total} 条)
            </h3>
          </div>
        </div>

        <Table
          columns={columns}
          data={questions}
          loading={loading}
          emptyText="暂无问题数据"
          rowKey="_id"
        />

        {total > 0 && (
          <Pagination
            current={currentPage}
            total={total}
            pageSize={pageSize}
            onChange={setCurrentPage}
            onPageSizeChange={setPageSize}
            showSizeChanger
            showQuickJumper
            showTotal
          />
        )}
      </div>

      {/* 问题表单弹窗 */}
      <Modal
        isOpen={formModalOpen}
        onClose={() => setFormModalOpen(false)}
        title={editingQuestion ? '编辑问题' : '新增问题'}
        size="lg"
      >
        <QuestionForm
          question={editingQuestion}
          onSuccess={handleFormSuccess}
          onCancel={() => setFormModalOpen(false)}
        />
      </Modal>

      {/* 问题详情弹窗 */}
      <Modal
        isOpen={detailModalOpen}
        onClose={() => setDetailModalOpen(false)}
        title="问题详情"
        size="lg"
      >
        {selectedQuestion && (
          <QuestionDetail
            question={selectedQuestion}
            onEdit={() => {
              setDetailModalOpen(false);
              handleEdit(selectedQuestion);
            }}
            onClose={() => setDetailModalOpen(false)}
          />
        )}
      </Modal>

      {/* 删除确认弹窗 */}
      <Modal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        title="确认删除"
        size="sm"
      >
        <ModalBody>
          <p className="text-sm text-gray-500">
            确定要删除这个问题吗？此操作不可撤销。
          </p>
          {selectedQuestion && (
            <div className="mt-3 p-3 bg-gray-50 rounded-md">
              <p className="text-sm font-medium text-gray-900">
                {selectedQuestion.sketch.zh_CN || selectedQuestion.sketch.en || '无标题'}
              </p>
            </div>
          )}
        </ModalBody>
        <ModalFooter>
          <Button
            variant="secondary"
            onClick={() => setDeleteModalOpen(false)}
          >
            取消
          </Button>
          <Button
            variant="danger"
            loading={deleting}
            onClick={confirmDelete}
          >
            确认删除
          </Button>
        </ModalFooter>
      </Modal>

      {/* 导出弹窗 */}
      <Modal
        isOpen={exportModalOpen}
        onClose={() => setExportModalOpen(false)}
        title="导出问题"
        size="sm"
      >
        <ModalBody>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              选择导出格式和选项：
            </p>
            <div className="space-y-3">
              <Button
                variant="primary"
                fullWidth
                onClick={() => handleExport('json', false)}
              >
                导出为 JSON（当前语言）
              </Button>
              <Button
                variant="secondary"
                fullWidth
                onClick={() => handleExport('json', true)}
              >
                导出为 JSON（所有语言）
              </Button>
              <Button
                variant="secondary"
                fullWidth
                onClick={() => handleExport('csv', false)}
              >
                导出为 CSV（当前语言）
              </Button>
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <Button
            variant="secondary"
            onClick={() => setExportModalOpen(false)}
          >
            取消
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
};

export default QuestionManagement;
