/**
 * 后台用户管理控制器
 * 提供用户查询、编辑、余额管理等功能
 */

import { Request, Response, NextFunction } from 'express';
import User from '../../models/User';
import { logger } from '../../business/logger';
import { validationResult } from 'express-validator';

export class UserController {
    /**
     * 获取用户列表
     */
    public async getUsers(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                page = 1,
                limit = 20,
                search,
                isVip,
                emailVerified,
                sortBy = 'createdAt',
                sortOrder = 'desc'
            } = req.query;

            // 构建查询条件
            const query: any = {};
            
            if (search) {
                query.$or = [
                    { email: { $regex: search, $options: 'i' } },
                    { fullName: { $regex: search, $options: 'i' } },
                    { username: { $regex: search, $options: 'i' } }
                ];
            }

            if (isVip !== undefined) {
                query.hasPurchase = isVip === 'true';
            }

            // 分页参数
            const pageNum = Math.max(1, parseInt(page as string));
            const limitNum = Math.min(100, Math.max(1, parseInt(limit as string)));
            const skip = (pageNum - 1) * limitNum;

            // 排序
            const sort: any = {};
            sort[sortBy as string] = sortOrder === 'asc' ? 1 : -1;

            // 查询用户
            const [users, total] = await Promise.all([
                User.find(query)
                    .select('-password')
                    .sort(sort)
                    .skip(skip)
                    .limit(limitNum)
                    .lean(),
                User.countDocuments(query)
            ]);

            res.json({
                success: true,
                data: {
                    users,
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total,
                        pages: Math.ceil(total / limitNum)
                    }
                }
            });

        } catch (error) {
            logger.error('Get users failed:', error);
            next(error);
        }
    }

    /**
     * 获取用户详情
     */
    public async getUserById(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { id } = req.params;

            const user = await User.findById(id).select('-password').lean();

            if (!user) {
                res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
                return;
            }

            res.json({
                success: true,
                data: user
            });

        } catch (error) {
            logger.error('Get user by ID failed:', error);
            next(error);
        }
    }

    /**
     * 更新用户信息
     */
    public async updateUser(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const { id } = req.params;
            const updateData = req.body;

            // 移除不允许直接更新的字段
            delete updateData.password;
            delete updateData.balance;
            delete updateData.totalSpent;
            delete updateData._id;
            delete updateData.createdAt;
            delete updateData.updatedAt;

            const user = await User.findByIdAndUpdate(
                id,
                updateData,
                { new: true, runValidators: true }
            ).select('-password');

            if (!user) {
                res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
                return;
            }

            logger.info(`User ${id} updated by admin`);

            res.json({
                success: true,
                data: user,
                message: 'User updated successfully'
            });

        } catch (error) {
            logger.error('Update user failed:', error);
            next(error);
        }
    }

    /**
     * 调整用户余额
     */
    public async adjustBalance(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const { id } = req.params;
            const { amount, reason, type } = req.body;

            const user = await User.findById(id);

            if (!user) {
                res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
                return;
            }

            let newBalance: number;

            if (type === 'add') {
                newBalance = user.balance + amount;
            } else if (type === 'deduct') {
                if (user.balance < amount) {
                    res.status(400).json({
                        success: false,
                        message: 'Insufficient balance'
                    });
                    return;
                }
                newBalance = user.balance - amount;
            } else if (type === 'set') {
                newBalance = amount;
            } else {
                res.status(400).json({
                    success: false,
                    message: 'Invalid operation type'
                });
                return;
            }

            const oldBalance = user.balance;
            user.balance = newBalance;
            await user.save();

            // 记录余额变更日志
            logger.info(`Balance adjusted for user ${id}: ${oldBalance} -> ${newBalance}, reason: ${reason}`);

            res.json({
                success: true,
                data: {
                    userId: id,
                    oldBalance,
                    newBalance,
                    amount,
                    type,
                    reason
                },
                message: 'Balance adjusted successfully'
            });

        } catch (error) {
            logger.error('Adjust balance failed:', error);
            next(error);
        }
    }

    /**
     * 设置VIP状态（使用hasPurchase字段）
     */
    public async setVipStatus(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { id } = req.params;
            const { isVip } = req.body;

            const user = await User.findById(id);

            if (!user) {
                res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
                return;
            }

            // 使用现有的hasPurchase字段来表示VIP状态
            (user as any).hasPurchase = isVip;

            await user.save();

            logger.info(`VIP status updated for user ${id}: ${isVip}`);

            res.json({
                success: true,
                data: {
                    userId: id,
                    isVip: (user as any).hasPurchase,
                    hasPurchase: (user as any).hasPurchase
                },
                message: 'VIP status updated successfully'
            });

        } catch (error) {
            logger.error('Set VIP status failed:', error);
            next(error);
        }
    }

    /**
     * 软删除用户
     */
    public async deleteUser(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { id } = req.params;

            const user = await User.findById(id);

            if (!user) {
                res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
                return;
            }

            user.isDelete = true;
            await user.save();

            logger.info(`User ${id} soft deleted by admin`);

            res.json({
                success: true,
                message: 'User deleted successfully'
            });

        } catch (error) {
            logger.error('Delete user failed:', error);
            next(error);
        }
    }

    /**
     * 恢复用户
     */
    public async restoreUser(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { id } = req.params;

            const user = await User.findById(id);

            if (!user) {
                res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
                return;
            }

            user.isDelete = false;
            await user.save();

            logger.info(`User ${id} restored by admin`);

            res.json({
                success: true,
                message: 'User restored successfully'
            });

        } catch (error) {
            logger.error('Restore user failed:', error);
            next(error);
        }
    }

    /**
     * 获取用户统计信息
     */
    public async getUserStats(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const [
                totalUsers,
                activeUsers,
                vipUsers,
                recentUsers
            ] = await Promise.all([
                User.countDocuments({}),
                User.countDocuments({ updatedAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } }),
                User.countDocuments({ hasPurchase: true }),
                User.countDocuments({ createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } })
            ]);

            const stats = {
                totalUsers,
                activeUsers,
                vipUsers,
                verifiedUsers: 0, // 现有模型没有emailVerified字段
                recentUsers,
                inactiveUsers: totalUsers - activeUsers,
                unverifiedUsers: totalUsers
            };

            res.json({
                success: true,
                data: stats
            });

        } catch (error) {
            logger.error('Get user stats failed:', error);
            next(error);
        }
    }

    /**
     * 批量删除用户
     */
    public async batchDeleteUsers(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const { userIds, permanent = false } = req.body;

            if (!Array.isArray(userIds) || userIds.length === 0) {
                res.status(400).json({
                    success: false,
                    message: 'User IDs array is required'
                });
                return;
            }

            let result;
            let affectedCount: number;

            if (permanent) {
                // 永久删除
                result = await User.deleteMany({ _id: { $in: userIds } });
                affectedCount = result.deletedCount || 0;
            } else {
                // 软删除
                result = await User.updateMany(
                    { _id: { $in: userIds } },
                    { isDelete: true }
                );
                affectedCount = result.modifiedCount || 0;
            }

            logger.info(`Batch ${permanent ? 'permanently ' : ''}deleted ${affectedCount} users`);

            res.json({
                success: true,
                data: {
                    affected: affectedCount,
                    total: userIds.length,
                    permanent
                },
                message: `Successfully ${permanent ? 'permanently ' : ''}deleted ${affectedCount} users`
            });

        } catch (error) {
            logger.error('Batch delete users failed:', error);
            next(error);
        }
    }

    /**
     * 批量更新用户状态
     */
    public async batchUpdateUserStatus(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const { userIds, updates } = req.body;

            if (!Array.isArray(userIds) || userIds.length === 0) {
                res.status(400).json({
                    success: false,
                    message: 'User IDs array is required'
                });
                return;
            }

            if (!updates || typeof updates !== 'object') {
                res.status(400).json({
                    success: false,
                    message: 'Updates object is required'
                });
                return;
            }

            // 过滤允许批量更新的字段
            const allowedFields = ['isDelete', 'hasPurchase', 'language', 'timeZone'];
            const filteredUpdates: any = {};

            for (const field of allowedFields) {
                if (updates.hasOwnProperty(field)) {
                    filteredUpdates[field] = updates[field];
                }
            }

            if (Object.keys(filteredUpdates).length === 0) {
                res.status(400).json({
                    success: false,
                    message: 'No valid fields to update'
                });
                return;
            }

            const result = await User.updateMany(
                { _id: { $in: userIds } },
                filteredUpdates
            );

            logger.info(`Batch updated ${result.modifiedCount} users with fields: ${Object.keys(filteredUpdates).join(', ')}`);

            res.json({
                success: true,
                data: {
                    affected: result.modifiedCount,
                    total: userIds.length,
                    updates: filteredUpdates
                },
                message: `Successfully updated ${result.modifiedCount} users`
            });

        } catch (error) {
            logger.error('Batch update user status failed:', error);
            next(error);
        }
    }

    /**
     * 批量调整用户余额
     */
    public async batchAdjustBalance(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const validationErrors = validationResult(req);
            if (!validationErrors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: validationErrors.array()
                });
                return;
            }

            const { userIds, amount, type, reason } = req.body;

            if (!Array.isArray(userIds) || userIds.length === 0) {
                res.status(400).json({
                    success: false,
                    message: 'User IDs array is required'
                });
                return;
            }

            if (typeof amount !== 'number' || amount <= 0) {
                res.status(400).json({
                    success: false,
                    message: 'Valid amount is required'
                });
                return;
            }

            if (!['add', 'deduct', 'set'].includes(type)) {
                res.status(400).json({
                    success: false,
                    message: 'Type must be add, deduct, or set'
                });
                return;
            }

            const results: any[] = [];
            const errors: Array<{userId: string, error: string}> = [];

            for (const userId of userIds) {
                try {
                    const user = await User.findById(userId);
                    if (!user) {
                        errors.push({ userId, error: 'User not found' });
                        continue;
                    }

                    const oldBalance = user.balance;
                    let newBalance: number;

                    switch (type) {
                        case 'add':
                            newBalance = oldBalance + amount;
                            break;
                        case 'deduct':
                            if (oldBalance < amount) {
                                errors.push({ userId, error: 'Insufficient balance' });
                                continue;
                            }
                            newBalance = oldBalance - amount;
                            break;
                        case 'set':
                            newBalance = amount;
                            break;
                        default:
                            errors.push({ userId, error: 'Invalid operation type' });
                            continue;
                    }

                    user.balance = newBalance;
                    await user.save();

                    results.push({
                        userId,
                        oldBalance,
                        newBalance,
                        amount,
                        type
                    });

                } catch (error) {
                    errors.push({ userId, error: error instanceof Error ? error.message : 'Unknown error' });
                }
            }

            logger.info(`Batch balance adjustment completed: ${results.length} successful, ${errors.length} failed`);

            res.json({
                success: true,
                data: {
                    successful: results,
                    failed: errors,
                    total: userIds.length,
                    successCount: results.length,
                    failCount: errors.length,
                    reason
                },
                message: `Balance adjustment completed: ${results.length} successful, ${errors.length} failed`
            });

        } catch (error) {
            logger.error('Batch adjust balance failed:', error);
            next(error);
        }
    }

    /**
     * 导出用户数据
     */
    public async exportUsers(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                format = 'csv',
                fields = [],
                search,
                isVip,
                dateFrom,
                dateTo,
                userIds
            } = req.query;

            // 构建查询条件
            const query: any = {};

            if (userIds && Array.isArray(userIds)) {
                query._id = { $in: userIds };
            } else {
                if (search) {
                    query.$or = [
                        { email: { $regex: search, $options: 'i' } },
                        { fullName: { $regex: search, $options: 'i' } },
                        { username: { $regex: search, $options: 'i' } }
                    ];
                }

                if (isVip !== undefined) {
                    query.hasPurchase = isVip === 'true';
                }

                if (dateFrom || dateTo) {
                    query.createdAt = {};
                    if (dateFrom) query.createdAt.$gte = new Date(dateFrom as string);
                    if (dateTo) query.createdAt.$lte = new Date(dateTo as string);
                }
            }

            // 查询用户数据
            const users = await User.find(query)
                .select('-password')
                .lean();

            // 定义导出字段
            const defaultFields = [
                'userId', 'email', 'fullName', 'gender', 'phone',
                'language', 'balance', 'hasPurchase', 'isDelete', 'createdAt'
            ];

            const exportFields: string[] = (Array.isArray(fields) && fields.length > 0) ? fields as string[] : defaultFields;

            // 格式化数据
            const formattedData = users.map(user => {
                const row: any = {};
                exportFields.forEach((field: string) => {
                    if (field === 'userId') {
                        row[field] = user._id.toString();
                    } else if (field === 'createdAt') {
                        row[field] = new Date((user as any).createdAt).toISOString();
                    } else {
                        row[field] = (user as any)[field] || '';
                    }
                });
                return row;
            });

            // 根据格式返回数据
            if (format === 'json') {
                res.json({
                    success: true,
                    data: formattedData,
                    total: formattedData.length
                });
            } else if (format === 'csv') {
                // 生成CSV
                const csv = this.generateCSV(formattedData, exportFields as string[]);
                const filename = `users_export_${new Date().toISOString().split('T')[0]}.csv`;

                res.setHeader('Content-Type', 'text/csv');
                res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
                res.send(csv);
            } else {
                res.status(400).json({
                    success: false,
                    message: 'Unsupported format. Use json or csv.'
                });
            }

            logger.info(`Exported ${formattedData.length} users in ${format} format`);

        } catch (error) {
            logger.error('Export users failed:', error);
            next(error);
        }
    }

    /**
     * 批量恢复用户
     */
    public async batchRestoreUsers(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { userIds } = req.body;

            if (!Array.isArray(userIds) || userIds.length === 0) {
                res.status(400).json({
                    success: false,
                    message: 'User IDs array is required'
                });
                return;
            }

            const result = await User.updateMany(
                { _id: { $in: userIds } },
                { isDelete: false }
            );

            logger.info(`Batch restored ${result.modifiedCount} users`);

            res.json({
                success: true,
                data: {
                    affected: result.modifiedCount,
                    total: userIds.length
                },
                message: `Successfully restored ${result.modifiedCount} users`
            });

        } catch (error) {
            logger.error('Batch restore users failed:', error);
            next(error);
        }
    }

    /**
     * 获取用户详细统计信息（包含时间范围）
     */
    public async getUserDetailedStats(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                dateFrom,
                dateTo,
                groupBy = 'day' // day, week, month
            } = req.query;

            const matchStage: any = {};
            if (dateFrom || dateTo) {
                matchStage.createdAt = {};
                if (dateFrom) matchStage.createdAt.$gte = new Date(dateFrom as string);
                if (dateTo) matchStage.createdAt.$lte = new Date(dateTo as string);
            }

            // 根据groupBy参数设置分组格式
            let dateFormat: string;
            switch (groupBy) {
                case 'week':
                    dateFormat = '%Y-%U'; // 年-周
                    break;
                case 'month':
                    dateFormat = '%Y-%m'; // 年-月
                    break;
                default:
                    dateFormat = '%Y-%m-%d'; // 年-月-日
            }

            const pipeline = [
                { $match: matchStage },
                {
                    $group: {
                        _id: {
                            date: { $dateToString: { format: dateFormat, date: '$createdAt' } },
                            isVip: '$hasPurchase'
                        },
                        count: { $sum: 1 },
                        totalBalance: { $sum: '$balance' }
                    }
                },
                {
                    $group: {
                        _id: '$_id.date',
                        totalUsers: { $sum: '$count' },
                        vipUsers: {
                            $sum: {
                                $cond: [{ $eq: ['$_id.isVip', true] }, '$count', 0]
                            }
                        },
                        regularUsers: {
                            $sum: {
                                $cond: [{ $eq: ['$_id.isVip', false] }, '$count', 0]
                            }
                        },
                        totalBalance: { $sum: '$totalBalance' }
                    }
                },
                { $sort: { _id: 1 as 1 } }
            ];

            const stats = await User.aggregate(pipeline);

            res.json({
                success: true,
                data: {
                    stats,
                    groupBy,
                    dateRange: {
                        from: dateFrom,
                        to: dateTo
                    }
                }
            });

        } catch (error) {
            logger.error('Get user detailed stats failed:', error);
            next(error);
        }
    }

    /**
     * 生成CSV格式数据
     */
    private generateCSV(data: any[], fields: string[]): string {
        if (data.length === 0) return '';

        // 生成表头
        const headers = fields.map(field => `"${field}"`).join(',');

        // 生成数据行
        const rows = data.map(row => {
            return fields.map(field => {
                const value = row[field];
                if (value === null || value === undefined) return '""';
                if (typeof value === 'string') {
                    // 转义双引号并包装在双引号中
                    return `"${value.replace(/"/g, '""')}"`;
                }
                return `"${value}"`;
            }).join(',');
        });

        return [headers, ...rows].join('\n');
    }

    /**
     * 验证批量操作权限
     */
    private async validateBatchOperation(userIds: string[], operation: string): Promise<{ valid: boolean; message?: string }> {
        // 检查用户ID数量限制
        if (userIds.length > 1000) {
            return { valid: false, message: 'Cannot process more than 1000 users at once' };
        }

        // 检查是否包含超级管理员账户（如果有的话）
        const superAdminEmails = ['<EMAIL>', '<EMAIL>'];
        const superAdmins = await User.find({
            _id: { $in: userIds },
            email: { $in: superAdminEmails }
        });

        if (superAdmins.length > 0 && ['delete', 'batchDelete'].includes(operation)) {
            return { valid: false, message: 'Cannot delete super admin accounts' };
        }

        return { valid: true };
    }
}

export default new UserController();
