/**
 * 余额交易记录管理控制器
 * 提供交易记录的查询、统计、分析等功能
 */

import { Request, Response, NextFunction } from 'express';
import BalanceTransaction, { TransactionType } from '../../models/BalanceTransaction';
import User from '../../models/User';
import Pricing from '../../models/Pricing';
import { logger } from '../../business/logger';
import { validationResult } from 'express-validator';

export class BalanceTransactionController {
    /**
     * 获取交易记录列表
     */
    public async getTransactions(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                page = 1,
                limit = 20,
                userId,
                type,
                dateFrom,
                dateTo,
                amountMin,
                amountMax,
                reason,
                sortBy = 'timestamp',
                sortOrder = 'desc'
            } = req.query;

            // 构建查询条件
            const query: any = {};
            
            if (userId) {
                query.userId = userId;
            }

            if (type && Object.values(TransactionType).includes(parseInt(type as string))) {
                query.type = parseInt(type as string);
            }

            if (dateFrom || dateTo) {
                query.timestamp = {};
                if (dateFrom) query.timestamp.$gte = new Date(dateFrom as string);
                if (dateTo) query.timestamp.$lte = new Date(dateTo as string);
            }

            if (amountMin || amountMax) {
                query.amount = {};
                if (amountMin) query.amount.$gte = parseFloat(amountMin as string);
                if (amountMax) query.amount.$lte = parseFloat(amountMax as string);
            }

            if (reason) {
                query.reason = { $regex: reason, $options: 'i' };
            }

            // 分页参数
            const pageNum = Math.max(1, parseInt(page as string));
            const limitNum = Math.min(100, Math.max(1, parseInt(limit as string)));
            const skip = (pageNum - 1) * limitNum;

            // 排序
            const sort: any = {};
            sort[sortBy as string] = sortOrder === 'asc' ? 1 : -1;

            // 查询交易记录
            const [transactions, total] = await Promise.all([
                BalanceTransaction.find(query)
                    .populate('modelId', 'modelName alias')
                    .sort(sort)
                    .skip(skip)
                    .limit(limitNum)
                    .lean(),
                BalanceTransaction.countDocuments(query)
            ]);

            // 获取用户信息
            const userIds = [...new Set(transactions.map(t => t.userId))];
            const users = await User.find({ _id: { $in: userIds } })
                .select('_id email fullName')
                .lean();
            
            const userMap = new Map(users.map(u => [u._id.toString(), u]));

            // 组合数据
            const enrichedTransactions = transactions.map(transaction => ({
                ...transaction,
                user: userMap.get(transaction.userId) || null,
                typeLabel: this.getTransactionTypeLabel(transaction.type)
            }));

            res.json({
                success: true,
                data: {
                    transactions: enrichedTransactions,
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total,
                        pages: Math.ceil(total / limitNum)
                    }
                }
            });

        } catch (error) {
            logger.error('Get transactions failed:', error);
            next(error);
        }
    }

    /**
     * 获取交易记录详情
     */
    public async getTransactionById(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { id } = req.params;

            const transaction = await BalanceTransaction.findById(id)
                .populate('modelId', 'modelName alias intro')
                .lean();

            if (!transaction) {
                res.status(404).json({
                    success: false,
                    message: 'Transaction not found'
                });
                return;
            }

            // 获取用户信息
            const user = await User.findById((transaction as any).userId)
                .select('email fullName balance')
                .lean();

            const enrichedTransaction = {
                ...transaction,
                user,
                typeLabel: this.getTransactionTypeLabel((transaction as any).type)
            };

            res.json({
                success: true,
                data: enrichedTransaction
            });

        } catch (error) {
            logger.error('Get transaction by ID failed:', error);
            next(error);
        }
    }

    /**
     * 获取用户交易历史
     */
    public async getUserTransactions(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { userId } = req.params;
            const {
                page = 1,
                limit = 20,
                type,
                dateFrom,
                dateTo
            } = req.query;

            // 验证用户是否存在
            const user = await User.findById(userId).select('email fullName balance');
            if (!user) {
                res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
                return;
            }

            // 构建查询条件
            const query: any = { userId };
            
            if (type && Object.values(TransactionType).includes(parseInt(type as string))) {
                query.type = parseInt(type as string);
            }

            if (dateFrom || dateTo) {
                query.timestamp = {};
                if (dateFrom) query.timestamp.$gte = new Date(dateFrom as string);
                if (dateTo) query.timestamp.$lte = new Date(dateTo as string);
            }

            // 分页参数
            const pageNum = Math.max(1, parseInt(page as string));
            const limitNum = Math.min(100, Math.max(1, parseInt(limit as string)));
            const skip = (pageNum - 1) * limitNum;

            // 查询交易记录
            const [transactions, total, summary] = await Promise.all([
                BalanceTransaction.find(query)
                    .populate('modelId', 'modelName alias')
                    .sort({ timestamp: -1 })
                    .skip(skip)
                    .limit(limitNum)
                    .lean(),
                BalanceTransaction.countDocuments(query),
                this.getUserTransactionSummary(userId, dateFrom as string, dateTo as string)
            ]);

            const enrichedTransactions = transactions.map(transaction => ({
                ...transaction,
                typeLabel: this.getTransactionTypeLabel(transaction.type)
            }));

            res.json({
                success: true,
                data: {
                    user: {
                        id: user._id,
                        email: user.email,
                        fullName: user.fullName,
                        currentBalance: user.balance
                    },
                    transactions: enrichedTransactions,
                    summary,
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total,
                        pages: Math.ceil(total / limitNum)
                    }
                }
            });

        } catch (error) {
            logger.error('Get user transactions failed:', error);
            next(error);
        }
    }

    /**
     * 获取交易统计信息
     */
    public async getTransactionStats(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                dateFrom,
                dateTo,
                groupBy = 'day' // day, week, month
            } = req.query;

            const matchStage: any = {};
            if (dateFrom || dateTo) {
                matchStage.timestamp = {};
                if (dateFrom) matchStage.timestamp.$gte = new Date(dateFrom as string);
                if (dateTo) matchStage.timestamp.$lte = new Date(dateTo as string);
            }

            // 根据groupBy参数设置分组格式
            let dateFormat: string;
            switch (groupBy) {
                case 'week':
                    dateFormat = '%Y-%U';
                    break;
                case 'month':
                    dateFormat = '%Y-%m';
                    break;
                default:
                    dateFormat = '%Y-%m-%d';
            }

            const [
                totalStats,
                typeDistribution,
                timeSeriesData,
                topUsers,
                modelUsage
            ] = await Promise.all([
                // 总体统计
                BalanceTransaction.aggregate([
                    { $match: matchStage },
                    {
                        $group: {
                            _id: null,
                            totalTransactions: { $sum: 1 },
                            totalAmount: { $sum: '$amount' },
                            avgAmount: { $avg: '$amount' },
                            deposits: {
                                $sum: {
                                    $cond: [{ $eq: ['$type', TransactionType.Deposit] }, '$amount', 0]
                                }
                            },
                            consumptions: {
                                $sum: {
                                    $cond: [{ $eq: ['$type', TransactionType.Out] }, '$amount', 0]
                                }
                            }
                        }
                    }
                ]),

                // 交易类型分布
                BalanceTransaction.aggregate([
                    { $match: matchStage },
                    {
                        $group: {
                            _id: '$type',
                            count: { $sum: 1 },
                            totalAmount: { $sum: '$amount' }
                        }
                    }
                ]),

                // 时间序列数据
                BalanceTransaction.aggregate([
                    { $match: matchStage },
                    {
                        $group: {
                            _id: {
                                date: { $dateToString: { format: dateFormat, date: '$timestamp' } },
                                type: '$type'
                            },
                            count: { $sum: 1 },
                            amount: { $sum: '$amount' }
                        }
                    },
                    {
                        $group: {
                            _id: '$_id.date',
                            totalTransactions: { $sum: '$count' },
                            totalAmount: { $sum: '$amount' },
                            deposits: {
                                $sum: {
                                    $cond: [{ $eq: ['$_id.type', TransactionType.Deposit] }, '$amount', 0]
                                }
                            },
                            consumptions: {
                                $sum: {
                                    $cond: [{ $eq: ['$_id.type', TransactionType.Out] }, '$amount', 0]
                                }
                            }
                        }
                    },
                    { $sort: { _id: 1 } }
                ]),

                // 交易量最高的用户
                BalanceTransaction.aggregate([
                    { $match: matchStage },
                    {
                        $group: {
                            _id: '$userId',
                            transactionCount: { $sum: 1 },
                            totalAmount: { $sum: '$amount' }
                        }
                    },
                    { $sort: { totalAmount: -1 } },
                    { $limit: 10 }
                ]),

                // 模型使用统计
                BalanceTransaction.aggregate([
                    { 
                        $match: { 
                            ...matchStage, 
                            modelId: { $exists: true, $ne: null } 
                        } 
                    },
                    {
                        $lookup: {
                            from: 'pricings',
                            localField: 'modelId',
                            foreignField: '_id',
                            as: 'model'
                        }
                    },
                    { $unwind: '$model' },
                    {
                        $group: {
                            _id: '$modelId',
                            modelName: { $first: '$model.modelName' },
                            usageCount: { $sum: 1 },
                            totalAmount: { $sum: '$amount' }
                        }
                    },
                    { $sort: { usageCount: -1 } }
                ])
            ]);

            // 获取用户信息
            const userIds = topUsers.map(u => u._id);
            const users = await User.find({ _id: { $in: userIds } })
                .select('_id email fullName')
                .lean();
            
            const userMap = new Map(users.map(u => [u._id.toString(), u]));
            const enrichedTopUsers = topUsers.map(user => ({
                ...user,
                userInfo: userMap.get(user._id) || null
            }));

            res.json({
                success: true,
                data: {
                    overview: totalStats[0] || {
                        totalTransactions: 0,
                        totalAmount: 0,
                        avgAmount: 0,
                        deposits: 0,
                        consumptions: 0
                    },
                    typeDistribution: typeDistribution.map(item => ({
                        ...item,
                        typeLabel: this.getTransactionTypeLabel(item._id)
                    })),
                    timeSeriesData,
                    topUsers: enrichedTopUsers,
                    modelUsage,
                    groupBy,
                    dateRange: {
                        from: dateFrom,
                        to: dateTo
                    }
                }
            });

        } catch (error) {
            logger.error('Get transaction stats failed:', error);
            next(error);
        }
    }

    /**
     * 导出交易记录
     */
    public async exportTransactions(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                format = 'csv',
                userId,
                type,
                dateFrom,
                dateTo,
                includeUserInfo = 'true'
            } = req.query;

            // 构建查询条件
            const query: any = {};
            if (userId) query.userId = userId;
            if (type && Object.values(TransactionType).includes(parseInt(type as string))) {
                query.type = parseInt(type as string);
            }

            if (dateFrom || dateTo) {
                query.timestamp = {};
                if (dateFrom) query.timestamp.$gte = new Date(dateFrom as string);
                if (dateTo) query.timestamp.$lte = new Date(dateTo as string);
            }

            // 查询交易记录
            const transactions = await BalanceTransaction.find(query)
                .populate('modelId', 'modelName alias')
                .sort({ timestamp: -1 })
                .lean();

            // 获取用户信息（如果需要）
            let userMap = new Map();
            if (includeUserInfo === 'true') {
                const userIds = [...new Set(transactions.map(t => t.userId))];
                const users = await User.find({ _id: { $in: userIds } })
                    .select('_id email fullName')
                    .lean();
                userMap = new Map(users.map(u => [u._id.toString(), u]));
            }

            // 格式化数据
            const formattedData = transactions.map((transaction: any) => {
                const baseData = {
                    id: transaction._id.toString(),
                    userId: transaction.userId,
                    amount: transaction.amount,
                    reason: transaction.reason,
                    timestamp: new Date(transaction.timestamp).toISOString(),
                    type: transaction.type,
                    typeLabel: this.getTransactionTypeLabel(transaction.type),
                    modelName: transaction.modelId?.modelName || ''
                };

                if (includeUserInfo === 'true') {
                    const user = userMap.get(transaction.userId);
                    return {
                        ...baseData,
                        userEmail: user?.email || '',
                        userFullName: user?.fullName || ''
                    };
                }

                return baseData;
            });

            if (format === 'json') {
                res.json({
                    success: true,
                    data: formattedData,
                    total: formattedData.length
                });
            } else if (format === 'csv') {
                const csv = this.generateCSV(formattedData);
                const filename = `transactions_export_${new Date().toISOString().split('T')[0]}.csv`;

                res.setHeader('Content-Type', 'text/csv');
                res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
                res.send(csv);
            } else {
                res.status(400).json({
                    success: false,
                    message: 'Unsupported format. Use json or csv.'
                });
            }

            logger.info(`Exported ${formattedData.length} transactions in ${format} format`);

        } catch (error) {
            logger.error('Export transactions failed:', error);
            next(error);
        }
    }

    /**
     * 获取用户交易摘要
     */
    private async getUserTransactionSummary(userId: string, dateFrom?: string, dateTo?: string) {
        const matchStage: any = { userId };
        if (dateFrom || dateTo) {
            matchStage.timestamp = {};
            if (dateFrom) matchStage.timestamp.$gte = new Date(dateFrom);
            if (dateTo) matchStage.timestamp.$lte = new Date(dateTo);
        }

        const summary = await BalanceTransaction.aggregate([
            { $match: matchStage },
            {
                $group: {
                    _id: null,
                    totalTransactions: { $sum: 1 },
                    totalDeposits: {
                        $sum: {
                            $cond: [{ $eq: ['$type', TransactionType.Deposit] }, '$amount', 0]
                        }
                    },
                    totalConsumptions: {
                        $sum: {
                            $cond: [{ $eq: ['$type', TransactionType.Out] }, '$amount', 0]
                        }
                    },
                    depositCount: {
                        $sum: {
                            $cond: [{ $eq: ['$type', TransactionType.Deposit] }, 1, 0]
                        }
                    },
                    consumptionCount: {
                        $sum: {
                            $cond: [{ $eq: ['$type', TransactionType.Out] }, 1, 0]
                        }
                    }
                }
            }
        ]);

        return summary[0] || {
            totalTransactions: 0,
            totalDeposits: 0,
            totalConsumptions: 0,
            depositCount: 0,
            consumptionCount: 0
        };
    }

    /**
     * 获取交易类型标签
     */
    private getTransactionTypeLabel(type: number): string {
        switch (type) {
            case TransactionType.Deposit:
                return '充值';
            case TransactionType.In:
                return '入账';
            case TransactionType.Out:
                return '消费';
            default:
                return '未知';
        }
    }

    /**
     * 生成CSV格式数据
     */
    private generateCSV(data: any[]): string {
        if (data.length === 0) return '';

        const fields = Object.keys(data[0]);

        // 生成表头
        const headers = fields.map(field => `"${field}"`).join(',');

        // 生成数据行
        const rows = data.map(row => {
            return fields.map(field => {
                const value = row[field];
                if (value === null || value === undefined) return '""';
                if (typeof value === 'string') {
                    return `"${value.replace(/"/g, '""')}"`;
                }
                return `"${value}"`;
            }).join(',');
        });

        return [headers, ...rows].join('\n');
    }
}

export default new BalanceTransactionController();
