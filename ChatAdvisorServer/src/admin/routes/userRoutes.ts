/**
 * 后台用户管理路由
 */

import { Router } from 'express';
import { body, param, query } from 'express-validator';
import UserController from '../controllers/UserController';
import { adminAuth } from '../middleware/adminAuth';
import { validateRequest } from '../middleware/validateRequest';

const router = Router();

// 应用管理员认证中间件
router.use(adminAuth);

// 获取用户列表
router.get('/',
    [
        query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
        query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
        query('search').optional().isString().trim(),
        query('isVip').optional().isBoolean(),
        query('emailVerified').optional().isBoolean(),
        query('sortBy').optional().isIn(['createdAt', 'updatedAt', 'email', 'balance', 'lastLoginAt']),
        query('sortOrder').optional().isIn(['asc', 'desc'])
    ],
    validateRequest,
    UserController.getUsers
);

// 获取用户统计信息
router.get('/stats', UserController.getUserStats);

// 获取用户详情
router.get('/:id',
    [
        param('id').isMongoId().withMessage('Invalid user ID')
    ],
    validateRequest,
    UserController.getUserById
);

// 更新用户信息
router.put('/:id',
    [
        param('id').isMongoId().withMessage('Invalid user ID'),
        body('email').optional().isEmail().withMessage('Invalid email format'),
        body('fullName').optional().isString().trim().isLength({ min: 1, max: 100 }),
        body('phone').optional().isMobilePhone('any'),
        body('language').optional().isIn(['zh_CN', 'en_US', 'ja_JP', 'ko_KR']),
        body('currency').optional().isIn(['CNY', 'USD', 'EUR', 'JPY', 'KRW']),
        body('gender').optional().isIn(['Male', 'Female', 'Other']),
        body('occupation').optional().isString().trim(),
        body('company').optional().isString().trim()
    ],
    validateRequest,
    UserController.updateUser
);

// 调整用户余额
router.post('/:id/balance',
    [
        param('id').isMongoId().withMessage('Invalid user ID'),
        body('amount').isFloat({ min: 0 }).withMessage('Amount must be a positive number'),
        body('type').isIn(['add', 'deduct', 'set']).withMessage('Type must be add, deduct, or set'),
        body('reason').isString().trim().isLength({ min: 1, max: 200 }).withMessage('Reason is required')
    ],
    validateRequest,
    UserController.adjustBalance
);

// 设置VIP状态
router.post('/:id/vip',
    [
        param('id').isMongoId().withMessage('Invalid user ID'),
        body('isVip').isBoolean().withMessage('isVip must be a boolean'),
        body('vipExpiredAt').optional().isISO8601().withMessage('Invalid date format')
    ],
    validateRequest,
    UserController.setVipStatus
);

// 软删除用户
router.delete('/:id',
    [
        param('id').isMongoId().withMessage('Invalid user ID')
    ],
    validateRequest,
    UserController.deleteUser
);

// 恢复用户
router.post('/:id/restore',
    [
        param('id').isMongoId().withMessage('Invalid user ID')
    ],
    validateRequest,
    UserController.restoreUser
);

// 获取用户统计信息
router.get('/stats', UserController.getUserStats);

// 批量删除用户
router.post('/batch-delete',
    [
        body('userIds').isArray().withMessage('User IDs must be an array'),
        body('userIds.*').isMongoId().withMessage('Invalid user ID'),
        body('permanent').optional().isBoolean()
    ],
    validateRequest,
    UserController.batchDeleteUsers
);

// 批量更新用户状态
router.post('/batch-status',
    [
        body('userIds').isArray().withMessage('User IDs must be an array'),
        body('userIds.*').isMongoId().withMessage('Invalid user ID'),
        body('updates').isObject().withMessage('Updates must be an object')
    ],
    validateRequest,
    UserController.batchUpdateUserStatus
);

// 批量调整用户余额
router.post('/batch-balance',
    [
        body('userIds').isArray().withMessage('User IDs must be an array'),
        body('userIds.*').isMongoId().withMessage('Invalid user ID'),
        body('amount').isFloat({ min: 0 }).withMessage('Amount must be a positive number'),
        body('type').isIn(['add', 'deduct', 'set']).withMessage('Type must be add, deduct, or set'),
        body('reason').optional().isString().trim()
    ],
    validateRequest,
    UserController.batchAdjustBalance
);

// 导出用户数据
router.get('/export',
    [
        query('format').optional().isIn(['json', 'csv']),
        query('fields').optional().isArray(),
        query('search').optional().isString().trim(),
        query('isVip').optional().isBoolean(),
        query('dateFrom').optional().isISO8601().withMessage('Invalid date format'),
        query('dateTo').optional().isISO8601().withMessage('Invalid date format'),
        query('userIds').optional().isArray()
    ],
    validateRequest,
    UserController.exportUsers
);

// 批量恢复用户
router.post('/batch-restore',
    [
        body('userIds').isArray().withMessage('User IDs must be an array'),
        body('userIds.*').isMongoId().withMessage('Invalid user ID')
    ],
    validateRequest,
    UserController.batchRestoreUsers
);

// 获取用户详细统计信息
router.get('/detailed-stats',
    [
        query('dateFrom').optional().isISO8601().withMessage('Invalid date format'),
        query('dateTo').optional().isISO8601().withMessage('Invalid date format'),
        query('groupBy').optional().isIn(['day', 'week', 'month'])
    ],
    validateRequest,
    UserController.getUserDetailedStats
);

export default router;
