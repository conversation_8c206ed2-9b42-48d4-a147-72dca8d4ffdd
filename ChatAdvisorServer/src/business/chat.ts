import { Request, Response, NextFunction } from 'express';
import mongoose from 'mongoose';
import OpenAI from 'openai';
import { HttpsProxyAgent } from 'https-proxy-agent';
import Pricing from '../models/Pricing';
import UserBalance from '../models/Balance';
import { ChatMessageModel, Role } from '../models/ChatMessage';
import BalanceTransaction, { TransactionType } from '../models/BalanceTransaction';
import env from '../config/env';
import { randomUUID } from 'crypto';
import { logger } from './logger';
import { HttpStatusCode } from 'axios';
import { encoding_for_model, TiktokenModel } from 'tiktoken';
import OpenAIClientFactory from '../services/OpenAIClientFactory';
import AIConfigManager from '../services/AIConfigManager';

// 获取OpenAI客户端和模型信息（使用新的配置管理系统）
async function getOpenAIClientWithModel(): Promise<{
    client: OpenAI;
    modelName: string;
    configName: string;
    isActiveConfig: boolean;
}> {
    try {
        return await OpenAIClientFactory.getDefaultClientWithModel();
    } catch (error) {
        logger.error('获取OpenAI客户端失败，回退到环境变量配置:', error);

        // 回退到原有的环境变量配置
        const apiKey = process.env.OPENAI_API_KEY || '';
        const baseURL = process.env.OPENAI_BASE_URL || 'https://api.x.ai/v1/';
        const defaultModel = process.env.OPENAI_DEFAULT_MODEL || 'grok-2-latest';

        if (!apiKey) {
            throw new Error('未配置AI服务，请联系管理员');
        }

        logger.info('=== 使用环境变量配置 OpenAI 客户端 ===');
        logger.info(`Base URL: ${baseURL}`);
        logger.info(`API Key: ${apiKey.substring(0, 10)}...${apiKey.substring(apiKey.length - 4)}`);
        logger.info(`Default Model: ${defaultModel}`);

        // 配置代理支持
        let httpAgent: any = undefined;
        if (process.env.HTTPS_PROXY || process.env.HTTP_PROXY) {
            const proxyUrl = process.env.HTTPS_PROXY || process.env.HTTP_PROXY;
            logger.info(`Using proxy: ${proxyUrl}`);
            httpAgent = new HttpsProxyAgent(proxyUrl);
        }

        const client = new OpenAI({
            apiKey: apiKey,
            baseURL: baseURL,
            timeout: 60000,
            maxRetries: 3,
            httpAgent: httpAgent,
        });

        return {
            client,
            modelName: defaultModel,
            configName: '环境变量配置',
            isActiveConfig: false
        };
    }
}

// 获取OpenAI客户端（向后兼容）
async function getOpenAIClient(): Promise<OpenAI> {
    const configInfo = await getOpenAIClientWithModel();
    return configInfo.client;
}

async function chatWithOpenai(req: Request, res: Response, next: NextFunction) {
    // 设置SSE响应头
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Headers', 'Cache-Control');

    let uuid = randomUUID();
    const noBalanceResponse = [
        {
            id: uuid,
            object: 'chat.completion.chunk',
            created: Date.now(),
            choices: [
                {
                    index: 0,
                    delta: {
                        role: 'assistant',
                        content: req.t('no_balance_1')
                    },
                    logprobs: null,
                    finish_reason: "no_balance"
                }
            ]
        },
        {
            id: uuid,
            object: 'chat.completion.chunk',
            created: Date.now(),
            choices: [
                {
                    index: 0,
                    delta: {
                        role: 'assistant',
                        content: req.t('no_balance_2')
                    },
                    logprobs: null,
                    finish_reason: 'no_balance'
                }
            ]
        }
    ];

    try {
        // @ts-ignore
        const userId = req.token.userId; // 从 token 中获取用户 ID
        const { modelname, chatid, 'remote-recognize': remote_recognize } = req.headers;

        // 获取配置信息（包括正确的模型名称）
        const aiConfigInfo = await getOpenAIClientWithModel();

        // 尝试根据配置的模型名称查找价格信息
        let modelPrice = await Pricing.findOne({ modelName: aiConfigInfo.modelName });

        // 如果没有找到价格信息，尝试使用请求头中的模型名称（向后兼容）
        if (!modelPrice && modelname) {
            modelPrice = await Pricing.findOne({ modelName: modelname });
        }

        // 如果仍然没有找到，使用默认的价格信息
        if (!modelPrice) {
            logger.warn(`未找到模型 ${aiConfigInfo.modelName} 的价格信息，使用默认价格`);
            // 创建一个临时的价格对象用于计算
            const defaultPricing = {
                modelName: aiConfigInfo.modelName,
                count: 1000,
                inPrice: 0.001,
                outPrice: 0.002
            };
            // 使用默认价格进行后续计算
            modelPrice = defaultPricing as any;
        }

        const modelName = modelPrice.modelName as TiktokenModel;
        // gpt-4o 和 gpt-4o-mini 使用相同的编码
        const encoding = encoding_for_model("gpt-4o");


        // 获取消息数组，支持新的请求格式
        const messages = req.body.messages || req.body;

        let tokenLength = 0;
        if (remote_recognize === '1') {
            logger.debug("remote-recognize", remote_recognize);
            messages.forEach((msg: any) => {
                if (msg.role === 'system') {
                    tokenLength += encoding.encode(msg.content).length;
                } else if (msg.role === 'user') {
                    const totalCost = msg.total_token_cost;
                    const contentCost = msg.content.reduce((acc: number, item: any) => {
                        if (item.type === 'image_url') {
                            if (item.token_cost != calculateImageTokens(item.width, item.height, "low")) {
                                return -1
                            }
                            return acc + item.token_cost;
                        }
                        return acc;
                    }, 0);
                    if (totalCost !== contentCost) {
                        throw new Error('token_cost_mismatch');
                    }
                    const testCost = msg.content.reduce((acc: number, item: any) => {
                        if (item.type === 'text') {
                            return acc + encoding.encode(item.text).length;
                        }
                        return acc;
                    }, 0);
                    tokenLength += contentCost;
                    tokenLength += testCost;
                }
            });
        } else {
            tokenLength = messages.reduce((acc: number, msg: any) => acc + encoding.encode(msg.content).length, 0);
        }

        const consumption = parseFloat(((tokenLength / modelPrice.count) * modelPrice.inPrice).toFixed(4));
        const userBalance = await UserBalance.findOne({ userId });

        if (!userBalance) {
            throw new Error('internal_server_error');
        }

        if (userBalance.balance < consumption) {
            logger.debug("余额不足");
            for (const item of noBalanceResponse) {
                res.write(`data: ${JSON.stringify(item)}\n\n`);
            }
            res.end();
            return;
        }

        const updatedBalance = await UserBalance.findOneAndUpdate(
            { userId, balance: userBalance.balance },
            { $inc: { balance: -consumption } },
            { new: true }
        );
        if (!updatedBalance) {
            throw new Error('internal_server_error');
        }

        const balanceTransaction = new BalanceTransaction({
            userId,
            amount: -consumption,
            reason: 'Chat with OpenAI',
            timestamp: Date.now(),
            modelId: modelPrice._id,
            type: TransactionType.In
        });
        await balanceTransaction.save();

        // 数据清理
        const cleanedMessages = messages.map((msg: any) => {
            const { content, role } = msg;
            if (role === 'user' && msg.content && remote_recognize === '1') {
                const cleanedContent = msg.content.map((item: any) => {
                    if (item.type === 'image_url') {
                        // 仅保留 image_url 的 detail 和 url
                        return { type: 'image_url', image_url: item.image_url };
                    }
                    return item;
                });
                return { content: cleanedContent, role };
            }
            if(role === 'system') {
                return { content: `你是一个智能消息助手。我（用户，a）会将目标人物（b）发送给我的消息粘贴给你（以user角色）。你的任务是以我的身份（作为a），根据对话历史和以下## 上下文：${content}，模拟我的语气和风格，生成一段可以直接复制粘贴发送给b的回复，像是真人自然回应。
                    ## 你的任务：
                    1. 仔细分析对话历史，理解我与b的关系、沟通风格和语调。
                    2. 以我的身份回复，直接回应b发送的消息，内容需贴合语境，推动对话自然发展，避免不必要的提问，除非绝对需要。
                    3. 根据b的身份和情绪调整语气（例如：对老板正式、对朋友随意、对暧昧对象俏皮、对方不耐烦时稍带幽默或直接）。
                    4. 确保回复生动、自然、可直接复制，避免机械化重复、过多疑问或被动等待指示的语气。
                    5. 参考## 上下文：${content}作为对话方向的指引，但需灵活适应b的回应，不要固守单一话题或思路。
                    6. 如果b显得无聊、不耐烦或想转换话题，迅速调整策略，加入幽默、热情建议或顺应b的引导，同时保持我的意图。
                    7. 适当主动引导对话，提出符合## 上下文：${content}中情绪的建议或俏皮评论，但尊重b的明确意图。
                    8. 回复内容需与对话历史和我的意图一致，避免任何不符之处。
                    9. 回复长度默认简洁（约50-100字），除非我明确指定字数（如300字），并保持中文表达自然流畅。

                    ## 上下文：
                    - 对话情绪：（根据具体场景填写，例如“轻松友好”或“俏皮暧昧”）。
                    - 关系背景：（例如“b是我的好友，平时聊天随意，爱开玩笑”）。
                    - 其他指引：（例如“优先保持轻松氛围，避免过于严肃”）。`, role };
                    }
                    return { content, role };
            });

        let fullResponse = '';
        let messageId = '';
        logger.debug("cleanedMessages", cleanedMessages);

        // 获取配置信息（包括正确的模型名称）
        const streamConfigInfo = await getOpenAIClientWithModel();

        // 添加重试逻辑
        let retryCount = 0;
        const maxRetries = 3;
        let stream;

        while (retryCount <= maxRetries) {
            try {
                logger.info(`=== API Call Attempt ${retryCount + 1}/${maxRetries + 1} ===`);
                logger.info(`Config: ${streamConfigInfo.configName} (${streamConfigInfo.isActiveConfig ? '启用配置' : '默认配置'})`);
                logger.info(`Model: ${streamConfigInfo.modelName}`);
                logger.info(`Messages count: ${cleanedMessages.length}`);

                logger.info('Creating stream...');

                stream = streamConfigInfo.client.beta.chat.completions.stream({
                    model: streamConfigInfo.modelName,
                    stream: true,
                    messages: cleanedMessages,
                    frequency_penalty: 1.2,   // 提高到 1.2，进一步减少重复用词
                    temperature: 0.9,         // 调整到 0.9，平衡创造性与连贯性
                    presence_penalty: 1.2,    // 提高到 1.2，鼓励新话题和角度
                });

                logger.info('Stream created successfully');
                break; // 成功创建stream，跳出重试循环
            } catch (error) {
                retryCount++;
                logger.error(`=== API Call Failed (Attempt ${retryCount}/${maxRetries + 1}) ===`);
                logger.error(`Error Type: ${error.constructor.name}`);
                logger.error(`Error Message: ${error.message}`);
                logger.error(`Error Code: ${error.code || 'N/A'}`);
                logger.error(`Error Status: ${error.status || 'N/A'}`);

                if (error.cause) {
                    logger.error(`Cause Type: ${error.cause.type || 'N/A'}`);
                    logger.error(`Cause Code: ${error.cause.code || 'N/A'}`);
                    logger.error(`Cause Errno: ${error.cause.errno || 'N/A'}`);
                }

                logger.error('Full Error Object:', error);

                if (retryCount > maxRetries) {
                    logger.error(`所有重试已用尽，抛出错误`);
                    throw error; // 超过最大重试次数，抛出错误
                }

                const waitTime = 1000 * retryCount;
                logger.warn(`等待 ${waitTime}ms 后重试...`);
                // 等待一段时间后重试
                await new Promise(resolve => setTimeout(resolve, waitTime));
            }
        }
        // @ts-ignore
        for await (const chunk of stream.toReadableStream()) {
            const chunkString = Buffer.from(chunk).toString('utf-8');
            // logger.debug(`data: ${chunkString}\n\n`);
            try {
                const chunkObject = JSON.parse(chunkString);
                const deltaContent = chunkObject.choices[0].delta.content;
                messageId = chunkObject.id;
                if (deltaContent) {
                    fullResponse += deltaContent;
                }
                // 统一使用标准SSE格式
                res.write(`data: ${chunkString}\n\n`);
            } catch (error) {
                logger.error(`Error parsing JSON: ${error}`);
            }
        }
        // logger.debug("fullResponse", fullResponse);
        const responseLength = encoding.encode(fullResponse).length;
        logger.debug("responseLength", responseLength);
        const responseConsumption = parseFloat(((responseLength / modelPrice.count) * modelPrice.outPrice).toFixed(4));
        const responseUpdatedBalance = await UserBalance.findOneAndUpdate(
            { userId },
            { $inc: { balance: -responseConsumption } },
            { new: true }
        );
        if (!responseUpdatedBalance) {
            throw new Error('internal_server_error');
        }

        const responseBalanceTransaction = new BalanceTransaction({
            userId,
            amount: -responseConsumption,
            reason: 'Chat with OpenAI',
            timestamp: Date.now(),
            modelId: modelPrice._id,
            type: TransactionType.Out
        });
        await responseBalanceTransaction.save();

        if (fullResponse !== '') {
            await ChatMessageModel.create({
                id: messageId,
                chatId: chatid,
                createdTime: Date.now(),
                role: Role.ASSISTANT,
                content: fullResponse,
                isComplete: true
            });
        }

        // 记录AI配置使用日志
        try {
            const config = await AIConfigManager.getActiveConfig();
            await AIConfigManager.logUsage({
                configId: config._id.toString(),
                modelId: modelPrice._id.toString(),
                userId: userId,
                requestId: messageId || uuid,
                tokensUsed: tokenLength + responseLength,
                cost: consumption + responseConsumption,
                responseTime: Date.now() - Date.now(), // 这里需要记录实际的响应时间
                success: true
            });
        } catch (logError) {
            logger.error('记录AI配置使用日志失败:', logError);
        }

        res.end();
    } catch (e) {
        logger.error('=== FINAL ERROR CATCH ===');
        logger.error('Error caught in main try-catch:', e);

        // 详细的错误诊断
        if (e instanceof Error) {
            logger.error(`Error Name: ${e.name}`);
            logger.error(`Error Message: ${e.message}`);
            logger.error(`Error Stack: ${e.stack}`);

            // 检查是否是网络相关错误
            if ((e as any).cause) {
                logger.error(`Error Cause:`, (e as any).cause);
            }
        }

        // 输出当前配置用于诊断
        logger.error('=== Current Configuration ===');
        logger.error(`OPENAI_API_KEY: ${process.env.OPENAI_API_KEY ? `${process.env.OPENAI_API_KEY.substring(0, 10)}...` : 'NOT SET'}`);
        logger.error(`OPENAI_BASE_URL: ${process.env.OPENAI_BASE_URL || 'NOT SET'}`);
        logger.error(`NODE_ENV: ${process.env.NODE_ENV || 'NOT SET'}`);
        logger.error('============================');

        // 根据错误类型提供更友好的错误信息
        let errorMessage = 'internal_server_error';

        if (e instanceof Error) {
            if (e.message.includes('ECONNRESET') || e.message.includes('Connection error')) {
                errorMessage = '网络连接不稳定，请检查网络或API配置';
                logger.error('网络连接错误 - 可能的原因：');
                logger.error('1. API密钥无效');
                logger.error('2. 网络连接问题');
                logger.error('3. API服务不可用');
                logger.error('4. 防火墙阻止连接');
            } else if (e.message.includes('API key')) {
                errorMessage = 'API配置错误，请联系管理员';
            } else if (e.message.includes('timeout')) {
                errorMessage = '请求超时，请稍后重试';
            } else if (e.message.includes('rate limit')) {
                errorMessage = '请求过于频繁，请稍后重试';
            } else if (e.message.includes('insufficient_quota')) {
                errorMessage = 'API配额不足，请联系管理员';
            }
        }

        const errorResponse = {
            id: uuid,
            object: 'chat.completion.chunk',
            created: Date.now(),
            choices: [
                {
                    index: 0,
                    delta: {
                        role: 'assistant',
                        content: errorMessage
                    },
                    logprobs: null,
                    finish_reason: 'error'
                }
            ]
        };
        res.write(`data: ${JSON.stringify(errorResponse)}\n\n`);
        res.end();
    }
}

/**
 * 计算图像的token成本
 * @param width - 图像的宽度
 * @param height - 图像的高度
 * @param detail - 图像的详细程度 ('low' 或 'high')
 * @returns 返回图像的token成本
 */
function calculateImageTokens(width: number, height: number, detail: 'low' | 'high'): number {
    // detail: low 模式下，固定成本为 85 tokens
    if (detail === 'low') {
        return 85;
    }

    // 确保图像适应2048x2048的范围
    if (width > 2048 || height > 2048) {
        const scalingFactor = Math.min(2048 / width, 2048 / height);
        width = Math.floor(width * scalingFactor);
        height = Math.floor(height * scalingFactor);
    }

    // 确保图像的最短边为768px长
    if (width < 768 || height < 768) {
        const scalingFactor = Math.max(768 / width, 768 / height);
        width = Math.floor(width * scalingFactor);
        height = Math.floor(height * scalingFactor);
    }

    // 计算512px正方形的数量
    const numTiles = Math.ceil(width / 512) * Math.ceil(height / 512);

    // detail: high 模式下，成本为170 tokens每个512px正方形，加上85 tokens的固定成本
    return numTiles * 170 + 85;
}

export default chatWithOpenai;
