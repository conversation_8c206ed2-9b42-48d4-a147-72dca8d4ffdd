import Foundation
import Localize_Swift
import SimpleToast
import SwiftUI

struct ChatView: View {
    @Binding var showSideMenu: Bool
    @ObservedObject var bootManager = BootManager.shared
    @EnvironmentObject var viewModel: ChatViewModel
    @EnvironmentObject var contentViewModel: ContentViewModel

    var body: some View {
        VStack {
            // 新增：网络状态指示器
            NetworkStatusIndicator()

            ZStack(alignment: .center) {
                // 聊天详情加载指示器和错误显示（只在初始加载时显示）
                if contentViewModel.isLoadingChatDetails || viewModel.isLoadingInitialMessages {
                    VStack {
                        Spacer()
                        VStack(spacing: 16) {
                            ProgressView()
                                .scaleEffect(1.2)
                                .tint(.accentColor)
                            Text("正在加载聊天记录...")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(20)
                        .background(Color(.systemBackground).opacity(0.95))
                        .cornerRadius(16)
                        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
                        Spacer()
                    }
                    .transition(.opacity.combined(with: .scale))
                    .zIndex(1)
                }

                // 错误状态显示
                if let errorMessage = contentViewModel.chatLoadingError {
                    VStack {
                        Spacer()
                        VStack(spacing: 16) {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .font(.largeTitle)
                                .foregroundColor(.orange)
                            Text(errorMessage)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                            Button("重试") {
                                contentViewModel.chatLoadingError = nil
                            }
                            .buttonStyle(.borderedProminent)
                            .tint(.accentColor)
                        }
                        .padding(20)
                        .background(Color(.systemBackground).opacity(0.95))
                        .cornerRadius(16)
                        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
                        Spacer()
                    }
                    .transition(.opacity.combined(with: .scale))
                    .zIndex(1)
                }
                ScrollViewReader { proxy in
                    ScrollView {
                        scrollContentView
                        .onChange(of: viewModel.currentChat.messages.count) { newCount in
                            handleMessageCountChange(newCount: newCount, proxy: proxy)
                        }
                        .onChange(of: viewModel.currentChat.messages.last?.content) { newContent in
                            handleLastMessageContentChange(newContent: newContent, proxy: proxy)
                        }
                    }
                    .coordinateSpace(name: "scroll")
                    .animation(nil, value: viewModel.currentChat.messages.count)
                    .sheet(item: Binding(
                        get: { viewModel.selectedMessage },
                        set: { viewModel.selectMessage($0) }
                    )) { message in
                        TextSelectionView(message: Binding(
                            get: { viewModel.selectedMessage },
                            set: { viewModel.selectMessage($0) }
                        ))
                    }
                }
            }


            HStack {
                InputBottomView()
                    .environmentObject(viewModel)
            }
            .padding(.horizontal, AppThemes.padding / 2)
        }
        .simpleToast(isPresented: Binding(
            get: { viewModel.showToast },
            set: { _ in /* Toast会自动隐藏 */ }
        ), options: AppThemes.toastOptions) {
            toastLabel
        }
        // {{ AURA-X: Modify - 优化背景色，使用微信风格的聊天背景 }}
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(.systemGray6).opacity(0.3),
                    Color(.systemBackground)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .coordinateSpace(name: "frameLayer")
        .onAppear {
            handleOnAppear()
        }
        .onChange(of: contentViewModel.selectedChatID) { newChatID in
            // 当选中的会话ID改变时，确保消息内容正确加载
            if let newChatID = newChatID {
                // 如果当前ChatView显示的会话与选中的会话ID匹配，但消息为空，则加载消息
                if viewModel.currentChat.id == newChatID &&
                   viewModel.currentChat.messages.isEmpty &&
                   !viewModel.isLoadingInitialMessages &&
                   !viewModel.isLoadingMessage {
                    Task {
                        await viewModel.fetchCurrentChatMessages()
                    }
                }
                // 注意：ChatView的environmentObject会根据ContentView的逻辑自动更新
                // 所以会话ID不匹配是正常的过渡状态，不需要警告
            }
        }
    }

    // MARK: - 视图组件

    private var scrollContentView: some View {
        // {{ AURA-X: Modify - 优化消息列表布局，采用微信风格间距 }}
        LazyVStack(spacing: AppThemes.Chat.messageVerticalSpacing) {
            emptyStateView
            messageListView

            // {{ AURA-X: Modify - 移除独立的TypingIndicator，将集成到消息气泡中 }}
            // 只在需要时显示打字指示器
            if viewModel.aiTypingState.shouldShow {
                WeChatStyleTypingIndicator(state: viewModel.aiTypingState)
                    .id("typing-indicator")
                    .transition(.asymmetric(
                        insertion: .scale(scale: 0.8).combined(with: .opacity),
                        removal: .opacity
                    ))
            }

            ScrollPositionReader(
                onPositionChange: { isNearBottom in
                    viewModel.scrollManager.updateUserPosition(isNearBottom: isNearBottom)
                },
                onDetailedPositionChange: { position in
                    viewModel.scrollManager.updateScrollPosition(position)

                    if viewModel.scrollManager.shouldTriggerPreload {
                        viewModel.loadMoreIfNeeds()
                    }
                }
            )

            Color.clear
                .frame(height: 16) // 减少底部间距
                .id("bottom")
        }
        .padding(.horizontal, AppThemes.Chat.messageHorizontalPadding)
    }

    private var emptyStateView: some View {
        Group {
            if viewModel.currentChat.messages.filter({ $0.role != .system }).isEmpty && !viewModel.isLoadingInitialMessages {
                // {{ AURA-X: Modify - 优化空状态视图，使用微信风格设计 }}
                VStack(spacing: 24) {
                    Spacer()

                    // 聊天图标
                    ZStack {
                        Circle()
                            .fill(AppThemes.Chat.aiBubbleColor)
                            .frame(width: 80, height: 80)
                            .shadow(
                                color: .black.opacity(0.05),
                                radius: 8,
                                x: 0, y: 2
                            )

                        Image(systemName: "message.circle.fill")
                            .font(.system(size: 40))
                            .foregroundColor(AppThemes.Chat.userBubbleColor)
                    }

                    VStack(spacing: 12) {
                        Text("开始新的对话")
                            .font(.system(size: 20, weight: .semibold))
                            .foregroundColor(.primary)

                        Text("在下方输入框中输入消息开始聊天")
                            .font(.system(size: 16))
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .lineLimit(2)
                    }

                    Spacer()
                }
                .frame(maxWidth: .infinity)
                .padding(.horizontal, 32)
            }
        }
    }

    private var messageListView: some View {
        // {{ AURA-X: Modify - 优化消息列表，移除额外padding，使用微信风格布局 }}
        ForEach(viewModel.currentChat.messages.filter { $0.role != .system }, id: \.id) { message in
            WeChatStyleMessageBubble(message: message)
                .environmentObject(viewModel)
                .contextMenu {
                    weChatStyleContextMenu(for: message)
                }
                .id(message.id)
                .transition(.asymmetric(
                    insertion: .scale(scale: AppThemes.Chat.bubbleScaleEffect)
                        .combined(with: .opacity)
                        .animation(.easeOut(duration: AppThemes.Chat.messageAppearDuration)),
                    removal: .opacity
                ))
                .onAppear {
                    if message == viewModel.currentChat.messages.filter({ $0.role != .system }).first {
                        viewModel.loadMoreIfNeeds()
                    }
                }
        }
    }

    // {{ AURA-X: Modify - 创建微信风格的上下文菜单，优化图标和布局 }}
    @ViewBuilder
    private func weChatStyleContextMenu(for message: ChatMessage) -> some View {
        Button(action: {
            UIPasteboard.general.string = message.content
            // 添加触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
        }) {
            Label("复制".localized(), systemImage: "doc.on.doc.fill")
                .font(.system(size: 14, weight: .medium))
        }

        Button(action: {
            viewModel.readAloud(text: message.content)
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
        }) {
            Label("朗读".localized(), systemImage: "speaker.wave.3.fill")
                .font(.system(size: 14, weight: .medium))
        }

        Button(action: {
            viewModel.selectMessage(message)
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
        }) {
            Label("选择文本".localized(), systemImage: "text.cursor")
                .font(.system(size: 14, weight: .medium))
        }

        // 如果是AI消息，添加重新生成选项
        if message.role == .assistant {
            Divider()
            Button(action: {
                // TODO: 实现重新生成功能
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()
            }) {
                Label("重新生成", systemImage: "arrow.clockwise")
                    .font(.system(size: 14, weight: .medium))
            }
        }
    }

    private var toastLabel: some View {
        // {{ AURA-X: Modify - 优化Toast样式，使用微信风格设计 }}
        HStack(spacing: 8) {
            Image(systemName: viewModel.isRequestError ? "exclamationmark.triangle.fill" : "checkmark.circle.fill")
                .font(.system(size: 16, weight: .medium))

            Text(viewModel.toastMessage)
                .font(.system(size: 14, weight: .medium))
                .lineLimit(2)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: AppThemes.Chat.bubbleCornerRadius)
                .fill(viewModel.isRequestError ? Color.red.opacity(0.9) : AppThemes.Chat.userBubbleColor)
                .shadow(
                    color: .black.opacity(0.15),
                    radius: 8,
                    x: 0, y: 4
                )
        )
        .foregroundColor(.white)
        .padding(.top, 120)
    }

    private var helpLabel: some View {
        Label("chat_help".localized(), systemImage: "questionmark.circle")
            .padding()
            .background(viewModel.isRequestError ? Color.red.opacity(0.8) : Color.mainDark)
            .foregroundColor(.white)
            .cornerRadius(10)
            .padding(.top, 120)
    }

    // MARK: - 私有方法

    private func handleMessageCountChange(newCount: Int, proxy: ScrollViewProxy) {
        viewModel.handleMessageCountChange(newCount: newCount) {
            if viewModel.scrollManager.shouldAutoScroll {
                withAnimation(.easeOut(duration: 0.3)) {
                    proxy.scrollTo("bottom", anchor: .bottom)
                }
                viewModel.scrollManager.clearNewMessages()
            }
        }
    }

    private func handleLastMessageContentChange(newContent: String?, proxy: ScrollViewProxy) {
        viewModel.handleLastMessageContentChange(newContent: newContent) {
            if viewModel.scrollManager.shouldAutoScroll {
                withAnimation(.easeOut(duration: 0.2)) {
                    proxy.scrollTo("bottom", anchor: .bottom)
                }
            }
        }
    }

    private func handleOnAppear() {
        if ChatViewModel.allModels.count == 0 {
            viewModel.getPricing()
        } else {
            viewModel.currentModel = ChatViewModel.allModels.first ?? ChatsModel.default
        }
        Task {
            await viewModel.loadOlderMessages()
        }
    }

    private func scrollToBottomSmoothly(with proxy: ScrollViewProxy) {
        withAnimation(.easeInOut(duration: 0.25)) {
            proxy.scrollTo("bottom", anchor: .bottom)
        }
    }
}

// MARK: - 微信风格UI组件

// {{ AURA-X: Add - 微信风格消息气泡组件 }}
/// 微信风格消息气泡
struct WeChatStyleMessageBubble: View {
    let message: ChatMessage
    @Environment(\.colorScheme) private var colorScheme
    @EnvironmentObject var chatViewModel: ChatViewModel
    @State private var isPressed = false

    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            if message.role == .user {
                Spacer(minLength: UIScreen.main.bounds.width * (1 - AppThemes.Chat.messageBubbleMaxWidth))
                messageContent
            } else {
                messageContent
                Spacer(minLength: UIScreen.main.bounds.width * (1 - AppThemes.Chat.messageBubbleMaxWidth))
            }
        }
        .scaleEffect(isPressed ? AppThemes.Chat.bubbleScaleEffect : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .onLongPressGesture(minimumDuration: 0.1, maximumDistance: 10) {
            // 长按完成
        } onPressingChanged: { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        }
    }

    @ViewBuilder
    private var messageContent: some View {
        VStack(alignment: message.role == .user ? .trailing : .leading, spacing: 4) {
            // 消息气泡
            messageBubble

            // 消息状态指示器（集成在气泡内）
            if message.role == .assistant && !message.isComplete {
                aiStatusIndicator
            }
        }
    }

    @ViewBuilder
    private var messageBubble: some View {
        Text(displayContent)
            .font(.system(size: 16, weight: .regular))
            .foregroundColor(message.role == .user ? AppThemes.Chat.userTextColor : AppThemes.Chat.aiTextColor)
            .padding(AppThemes.Chat.bubbleInnerPadding)
            .background(
                RoundedRectangle(cornerRadius: AppThemes.Chat.bubbleCornerRadius)
                    .fill(message.role == .user ? AppThemes.Chat.userBubbleColor : AppThemes.Chat.aiBubbleColor)
                    .overlay(
                        // AI消息添加边框
                        message.role == .assistant ?
                        RoundedRectangle(cornerRadius: AppThemes.Chat.bubbleCornerRadius)
                            .stroke(AppThemes.Chat.aiBubbleBorderColor, lineWidth: 0.5) : nil
                    )
                    .shadow(
                        color: .black.opacity(AppThemes.Chat.bubbleShadowOpacity),
                        radius: AppThemes.Chat.bubbleShadowRadius,
                        x: 0, y: 1
                    )
            )
            .fixedSize(horizontal: false, vertical: true)
    }

    @ViewBuilder
    private var aiStatusIndicator: some View {
        if chatViewModel.aiTypingState != .idle {
            HStack(spacing: 4) {
                ForEach(0..<3, id: \.self) { index in
                    Circle()
                        .fill(AppThemes.Chat.aiTextColor.opacity(0.6))
                        .frame(width: 4, height: 4)
                        .scaleEffect(chatViewModel.aiTypingState == .typing ? 1.2 : 1.0)
                        .animation(
                            Animation.easeInOut(duration: 0.6)
                                .repeatForever()
                                .delay(Double(index) * 0.2),
                            value: chatViewModel.aiTypingState
                        )
                }
            }
            .padding(.horizontal, AppThemes.Chat.bubbleInnerPadding)
            .padding(.vertical, 6)
        }
    }

    /// 获取要显示的内容（支持打字机效果）
    private var displayContent: String {
        if message.role == .assistant && !message.isDisplayComplete {
            let streamingContent = chatViewModel.getStreamingMessageDisplayContent(for: message.id)
            return streamingContent.isEmpty ? message.content : streamingContent
        }
        return message.content
    }
}

// {{ AURA-X: Add - 微信风格打字指示器 }}
/// 微信风格打字指示器
struct WeChatStyleTypingIndicator: View {
    let state: AITypingState
    @State private var dotOpacity: [Double] = [0.3, 0.3, 0.3]
    @State private var animationTimer: Timer?

    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            // AI头像占位（可以后续添加）
            Circle()
                .fill(Color.gray.opacity(0.3))
                .frame(width: 32, height: 32)
                .overlay(
                    Image(systemName: "brain")
                        .font(.system(size: 16))
                        .foregroundColor(.gray)
                )

            // 打字指示器气泡
            VStack(alignment: .leading, spacing: 4) {
                // 状态文本
                Text(state.displayText)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.secondary)

                // 动画指示器
                HStack(spacing: 4) {
                    if state == .connecting {
                        ProgressView()
                            .scaleEffect(0.7)
                            .tint(AppThemes.Chat.aiTextColor)
                    } else {
                        ForEach(0..<3, id: \.self) { index in
                            Circle()
                                .fill(AppThemes.Chat.aiTextColor)
                                .frame(width: 6, height: 6)
                                .opacity(dotOpacity[index])
                        }
                    }
                }
            }
            .padding(AppThemes.Chat.bubbleInnerPadding)
            .background(
                RoundedRectangle(cornerRadius: AppThemes.Chat.bubbleCornerRadius)
                    .fill(AppThemes.Chat.aiBubbleColor)
                    .overlay(
                        RoundedRectangle(cornerRadius: AppThemes.Chat.bubbleCornerRadius)
                            .stroke(AppThemes.Chat.aiBubbleBorderColor, lineWidth: 0.5)
                    )
                    .shadow(
                        color: .black.opacity(AppThemes.Chat.bubbleShadowOpacity),
                        radius: AppThemes.Chat.bubbleShadowRadius,
                        x: 0, y: 1
                    )
            )

            Spacer(minLength: UIScreen.main.bounds.width * (1 - AppThemes.Chat.messageBubbleMaxWidth))
        }
        .onAppear {
            startDotAnimation()
        }
        .onDisappear {
            stopDotAnimation()
        }
    }

    private func startDotAnimation() {
        guard state == .thinking || state == .typing else { return }

        animationTimer = Timer.scheduledTimer(withTimeInterval: 0.6, repeats: true) { _ in
            for i in 0..<3 {
                DispatchQueue.main.asyncAfter(deadline: .now() + Double(i) * 0.2) {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        dotOpacity[i] = dotOpacity[i] == 0.3 ? 1.0 : 0.3
                    }
                }
            }
        }
    }

    private func stopDotAnimation() {
        animationTimer?.invalidate()
        animationTimer = nil
    }
}
