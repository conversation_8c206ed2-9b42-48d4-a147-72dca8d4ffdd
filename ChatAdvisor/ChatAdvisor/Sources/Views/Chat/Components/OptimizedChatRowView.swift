//
//  OptimizedChatRowView.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import SwiftUI

/// 优化的聊天行视图组件 - 现代化美观设计
struct OptimizedChatRowView: View {
    let chat: Chat
    let isSelected: Bool
    let onTap: () -> Void
    let onRename: () -> Void
    let onArchive: () -> Void
    let onDelete: () -> Void
    
    // {{ AURA: Add - UI状态管理 }}
    @State private var isHovering = false
    @State private var isPressed = false
    
    // {{ AURA: Add - 缓存计算结果以提高性能 }}
    private var displayTitle: String {
        chat.title.isEmpty ? "没有标题的会话".localized() : chat.title
    }
    
    private var lastMessagePreview: String? {
        // {{ AURA: Modify - 优化消息预览，避免每次重新计算 }}
        chat.messages.last(where: { $0.role == .user || $0.role == .assistant })?.content
    }
    
    // {{ AURA: Add - 动态颜色计算 }}
    private var backgroundColor: Color {
        if isSelected {
            return Color.accentColor.opacity(0.85)
        } else if isHovering {
            return Color.accentColor.opacity(0.08)
        } else {
            return Color.clear
        }
    }
    
    private var shadowColor: Color {
        isSelected ? Color.accentColor.opacity(0.3) : Color.clear
    }
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 14) {
                // {{ AURA: Modify - 现代化内容区域设计 }}
                VStack(alignment: .leading, spacing: 6) {
                    Text(displayTitle)
                        .font(.system(.body, design: .rounded, weight: isSelected ? .semibold : .medium))
                        .foregroundColor(isSelected ? .white : .primary)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .lineLimit(1)
                    
                    // {{ AURA: Modify - 优化消息预览样式 }}
                    if let preview = lastMessagePreview {
                        Text(preview)
                            .font(.system(.caption, design: .rounded))
                            .foregroundColor(isSelected ? .white.opacity(0.85) : .secondary)
                            .lineLimit(2)
                            .multilineTextAlignment(.leading)
                    }
                }
                
                Spacer(minLength: 8)
                
                // {{ AURA: Modify - 现代化选中指示器 }}
                if isSelected {
                    ZStack {
                        Circle()
                            .fill(Color.white.opacity(0.2))
                            .frame(width: 28, height: 28)
                        
                        Image(systemName: "checkmark")
                            .foregroundColor(.white)
                            .font(.system(size: 14, weight: .semibold))
                    }
                    .transition(.asymmetric(
                        insertion: .scale(scale: 0.1).combined(with: .opacity),
                        removal: .scale(scale: 0.1).combined(with: .opacity)
                    ))
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .contentShape(Rectangle())
        .padding(.vertical, 10)
        .padding(.horizontal, 14)
        .frame(minHeight: 68)
        .background(
            // {{ AURA: Modify - 现代化背景设计 }}
            RoundedRectangle(cornerRadius: 12)
                .fill(backgroundColor)
                .shadow(
                    color: shadowColor,
                    radius: isSelected ? 8 : 0,
                    x: 0,
                    y: isSelected ? 2 : 0
                )
        )
        .overlay(
            // {{ AURA: Modify - 精致的边框效果 }}
            RoundedRectangle(cornerRadius: 12)
                .stroke(
                    LinearGradient(
                        colors: isSelected ? [
                            Color.white.opacity(0.3),
                            Color.white.opacity(0.1)
                        ] : [Color.clear],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: isSelected ? 1 : 0
                )
        )
        .scaleEffect(isPressed ? 0.97 : 1.0)
        .contextMenu {
            contextMenuContent
        }
        // {{ AURA: Modify - 流畅的组合动画 }}
        .animation(.spring(response: 0.4, dampingFraction: 0.8, blendDuration: 0), value: isSelected)
        .animation(.easeInOut(duration: 0.15), value: isHovering)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        // {{ AURA: Add - 交互手势 }}
        .onTapGesture {
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = true
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = false
                }
            }
            onTap()
        }
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovering = hovering
            }
        }
    }
    
    // {{ AURA: Add - 分离上下文菜单内容，提高代码可读性 }}
    @ViewBuilder
    private var contextMenuContent: some View {
        Button(action: onRename) {
            Label("重命名".localized(), systemImage: "pencil")
        }
        
        Button(action: onArchive) {
            Label("归档".localized(), systemImage: "archivebox")
        }
        
        Button(action: onDelete) {
            Label("删除".localized(), systemImage: "trash")
        }
    }
}

/// 优化的分组头部视图 - 现代化设计
struct GroupHeaderView: View {
    let date: Date
    
    var body: some View {
        HStack {
            // {{ AURA: Add - 现代化分组标题设计 }}
            Text(date.chatDateLabel())
                .font(.system(.subheadline, design: .rounded, weight: .semibold))
                .foregroundColor(.primary.opacity(0.8))
            
            Spacer()
            
            // {{ AURA: Add - 装饰性分割线 }}
            Rectangle()
                .fill(Color.secondary.opacity(0.2))
                .frame(height: 1)
                .frame(maxWidth: .infinity)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            // {{ AURA: Add - 毛玻璃效果背景 }}
            Rectangle()
                .fill(.ultraThinMaterial)
                .opacity(0.8)
        )
    }
}
