//
//  AIStatusIntegrationTest.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import SwiftUI

// {{ AURA-X: Add - AI状态集成测试组件 }}
/// AI状态集成测试预览
struct AIStatusIntegrationTest: View {
    @State private var currentState: AITypingState = .idle
    @State private var testMessages: [ChatMessage] = [
        ChatMessage(
            id: "user1",
            chatId: "test",
            role: .user,
            content: "你好，请帮我解释一下人工智能",
            isComplete: true
        ),
        ChatMessage(
            id: "ai1",
            chatId: "test",
            role: .assistant,
            content: "", // 空内容，用于测试AI状态显示
            isComplete: false
        )
    ]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 状态控制面板
                VStack(spacing: 12) {
                    Text("AI状态集成测试")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text("当前状态: \(currentState.displayText)")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    // 状态切换按钮
                    HStack(spacing: 8) {
                        stateButton("空闲", .idle)
                        stateButton("连接中", .connecting)
                        stateButton("思考中", .thinking)
                        stateButton("输入中", .typing)
                    }
                    
                    // 内容控制
                    HStack(spacing: 8) {
                        Button("清空AI消息") {
                            if let index = testMessages.firstIndex(where: { $0.role == .assistant }) {
                                testMessages[index] = ChatMessage(
                                    id: testMessages[index].id,
                                    chatId: testMessages[index].chatId,
                                    role: .assistant,
                                    content: "",
                                    isComplete: false
                                )
                            }
                        }
                        .buttonStyle(.bordered)
                        
                        Button("添加AI内容") {
                            if let index = testMessages.firstIndex(where: { $0.role == .assistant }) {
                                testMessages[index] = ChatMessage(
                                    id: testMessages[index].id,
                                    chatId: testMessages[index].chatId,
                                    role: .assistant,
                                    content: "这是AI的回复内容...",
                                    isComplete: false
                                )
                            }
                        }
                        .buttonStyle(.bordered)
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                
                // 消息列表
                ScrollView {
                    LazyVStack(spacing: 0) {
                        ForEach(Array(testMessages.enumerated()), id: \.element.id) { index, message in
                            let previousMessage = index > 0 ? testMessages[index - 1] : nil
                            let isRoleChanged = previousMessage?.role != message.role
                            let topSpacing = index == 0 ? 0 : (isRoleChanged ? AppThemes.Chat.messageGroupSpacing : AppThemes.Chat.messageVerticalSpacing)
                            
                            WeChatStyleMessageBubble(message: message)
                                .environmentObject(TestChatViewModel(aiState: currentState))
                                .contextMenu {
                                    Button("复制") {
                                        UIPasteboard.general.string = message.content
                                    }
                                }
                                .padding(.top, topSpacing)
                        }
                        
                        Color.clear.frame(height: 20)
                    }
                    .padding(.horizontal, AppThemes.Chat.messageHorizontalPadding)
                }
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(.systemGray6).opacity(0.3),
                            Color(.systemBackground)
                        ]),
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
            }
            .navigationTitle("AI状态集成测试")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    private func stateButton(_ title: String, _ state: AITypingState) -> some View {
        Button(title) {
            withAnimation(.easeInOut(duration: 0.3)) {
                currentState = state
            }
        }
        .buttonStyle(.bordered)
        .foregroundColor(currentState == state ? .white : .primary)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(currentState == state ? AppThemes.Chat.userBubbleColor : Color.clear)
        )
    }
}

// 测试用的ChatViewModel，可以控制AI状态
class TestChatViewModel: ChatViewModel {
    private let testAIState: AITypingState
    
    init(aiState: AITypingState) {
        self.testAIState = aiState
        let mockStepFormViewModel = MultiStepFormViewModel()
        let mockChat = Chat(id: "test", messages: [], title: "测试聊天")
        super.init(stepFormViewModel: mockStepFormViewModel, currentChat: mockChat)
    }
    
    override var aiTypingState: AITypingState {
        return testAIState
    }
    
    override func getStreamingMessageDisplayContent(for messageId: String) -> String {
        return ""
    }
}

#Preview {
    AIStatusIntegrationTest()
}
