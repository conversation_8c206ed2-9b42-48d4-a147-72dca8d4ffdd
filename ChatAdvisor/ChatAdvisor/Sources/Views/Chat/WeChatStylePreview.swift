//
//  WeChatStylePreview.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import SwiftUI

// {{ AURA-X: Add - 微信风格聊天界面预览组件 }}
/// 微信风格聊天界面预览
struct WeChatStylePreview: View {
    @State private var messages: [ChatMessage] = [
        ChatMessage(
            id: "1",
            chatId: "preview",
            role: .user,
            content: "你好，请帮我解释一下什么是人工智能？",
            isComplete: true
        ),
        ChatMessage(
            id: "2",
            chatId: "preview",
            role: .assistant,
            content: "人工智能（Artificial Intelligence，简称AI）是计算机科学的一个分支，旨在创建能够执行通常需要人类智能的任务的系统。这包括学习、推理、问题解决、感知和语言理解等能力。",
            isComplete: true
        ),
        ChatMessage(
            id: "3",
            chatId: "preview",
            role: .user,
            content: "能举个具体的例子吗？",
            isComplete: true
        ),
        ChatMessage(
            id: "4",
            chatId: "preview",
            role: .assistant,
            content: "当然可以！比如：\n\n1. **语音助手**：如Siri、小爱同学，能理解语音并回答问题\n2. **推荐系统**：如抖音、淘宝的个性化推荐\n3. **自动驾驶**：特斯拉等车辆的自动驾驶功能\n4. **图像识别**：手机相册自动分类照片\n\n这些都是AI在日常生活中的应用。",
            isComplete: false
        )
    ]
    
    @State private var aiTypingState: AITypingState = .typing
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 聊天内容区域
                ScrollView {
                    LazyVStack(spacing: AppThemes.Chat.messageVerticalSpacing) {
                        ForEach(messages, id: \.id) { message in
                            WeChatStyleMessageBubble(message: message)
                                .environmentObject(MockChatViewModel())
                        }
                        
                        // 打字指示器
                        if aiTypingState != .idle {
                            WeChatStyleTypingIndicator(state: aiTypingState)
                                .transition(.asymmetric(
                                    insertion: .scale(scale: 0.8).combined(with: .opacity),
                                    removal: .opacity
                                ))
                        }
                        
                        Color.clear.frame(height: 16)
                    }
                    .padding(.horizontal, AppThemes.Chat.messageHorizontalPadding)
                }
                
                // 输入区域占位
                HStack {
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color(.systemGray6))
                        .frame(height: 40)
                        .overlay(
                            Text("输入消息...")
                                .foregroundColor(.secondary)
                                .font(.system(size: 16))
                        )
                    
                    Button(action: {}) {
                        Circle()
                            .fill(AppThemes.Chat.userBubbleColor)
                            .frame(width: 40, height: 40)
                            .overlay(
                                Image(systemName: "paperplane.fill")
                                    .foregroundColor(.white)
                                    .font(.system(size: 16))
                            )
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color(.systemBackground))
            }
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(.systemGray6).opacity(0.3),
                        Color(.systemBackground)
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
            .navigationTitle("微信风格预览")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("切换状态") {
                        withAnimation {
                            switch aiTypingState {
                            case .idle:
                                aiTypingState = .connecting
                            case .connecting:
                                aiTypingState = .thinking
                            case .thinking:
                                aiTypingState = .typing
                            case .typing:
                                aiTypingState = .idle
                            }
                        }
                    }
                    .font(.system(size: 14))
                }
            }
        }
    }
}

// 模拟的ChatViewModel用于预览
class MockChatViewModel: ChatViewModel {
    override init() {
        // 创建模拟的依赖项
        let mockStepFormViewModel = MultiStepFormViewModel()
        let mockChat = Chat(id: "preview", messages: [], title: "预览聊天")
        
        super.init(stepFormViewModel: mockStepFormViewModel, currentChat: mockChat)
    }
    
    override func getStreamingMessageDisplayContent(for messageId: String) -> String {
        // 返回模拟的流式内容
        return "这是模拟的流式内容..."
    }
}

#Preview {
    WeChatStylePreview()
}
